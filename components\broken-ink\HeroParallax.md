# HeroParallax Component

O componente `HeroParallax` é uma versão aprimorada do `HeroSection` original que implementa um efeito de parallax personalizado baseado no scroll da página, inspirado na implementação do ref-parallax.

## Características

- **Efeito Parallax Personalizado**: Utiliza o `ScrollObserver` para criar um efeito de parallax suave baseado na posição do scroll
- **Conte<PERSON>do <PERSON>â<PERSON>**: Mantém toda a funcionalidade do HeroSection original (botões, WhatsApp, navegação)
- **Performance Otimizada**: Usa `useContext` e `useRef` para otimizar re-renders
- **Responsivo**: Funciona perfeitamente em dispositivos móveis e desktop

## Diferenças do HeroSection Original

### HeroSection (Original)
- Usa a biblioteca `react-parallax`
- Efeito de parallax baseado em biblioteca externa
- Blur e strength configuráveis

### HeroParallax (Novo)
- Implementação de parallax customizada
- Controle total sobre o efeito de parallax
- Baseado no scroll observer pattern
- Múltiplas camadas de parallax (background, conteúdo)

## Como Usar

### 1. Configurar o ScrollObserver

O componente precisa estar envolvido pelo `ScrollObserver` para funcionar:

```tsx
import ScrollObserver from "@/lib/scroll-observer";
import HeroParallax from "@/components/broken-ink/HeroParallax";

function App() {
  return (
    <ScrollObserver>
      <HeroParallax profile={userProfile} />
      {/* Outros componentes */}
    </ScrollObserver>
  );
}
```

### 2. Usar o Componente

```tsx
import HeroParallax from "@/components/broken-ink/HeroParallax";
import { UserProfile } from "@/types/user";

interface Props {
  profile: UserProfile;
}

function MyPage({ profile }: Props) {
  return (
    <div>
      <HeroParallax profile={profile} />
      {/* Conteúdo adicional */}
    </div>
  );
}
```

### 3. Exemplo Completo

Veja o arquivo `HeroParallaxExample.tsx` para um exemplo completo de como integrar o componente em uma página.

## Propriedades

| Prop | Tipo | Descrição |
|------|------|-----------|
| `profile` | `UserProfile` | Dados do perfil do usuário contendo informações para o hero |

## Efeitos de Parallax

O componente implementa diferentes velocidades de parallax para criar profundidade:

- **Background**: Move mais lentamente (`progress * 30vh`)
- **Título**: Move em velocidade média (`progress * -50vh`)
- **Descrição**: Move um pouco mais rápido (`progress * -40vh`)
- **Botões**: Move mais rápido (`progress * -30vh`)
- **Container**: Move lentamente para baixo (`progress * 20vh`)

## Dependências

- `@/lib/scroll-observer`: Para tracking do scroll
- `@/lib/brokenInkUtils`: Para transformação dos dados
- `@/components/ui/button`: Para os botões de ação
- `@/lib/iconUtils`: Para ícones (WhatsApp)

## Performance

- Usa `useRef` para evitar re-renders desnecessários
- Cálculos de parallax otimizados
- Scroll listener passivo para melhor performance

## Compatibilidade

- ✅ React 18+
- ✅ Next.js 13+
- ✅ TypeScript
- ✅ Mobile e Desktop
- ✅ Todos os navegadores modernos

## Troubleshooting

### O parallax não funciona
- Verifique se o `ScrollObserver` está envolvendo o componente
- Certifique-se de que há conteúdo suficiente na página para gerar scroll

### Performance lenta
- Verifique se não há múltiplos scroll listeners na página
- Use `React.memo` se necessário para otimizar re-renders

### Conteúdo não aparece
- Verifique se os dados do `profile` estão sendo passados corretamente
- Confirme se as imagens estão acessíveis
