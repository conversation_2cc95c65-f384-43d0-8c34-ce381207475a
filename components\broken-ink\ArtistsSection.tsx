import React, { useCallback, useEffect, useState } from "react";
import ArtistProfile from "./ArtistProfile";
import { UserProfile } from "@/types/user";
import { transformArtistsData } from "@/lib/brokenInkUtils";
import useEmblaCarousel from "embla-carousel-react";

interface ArtistsSectionProps {
  profile: UserProfile;
}

const ArtistsSection = ({ profile }: ArtistsSectionProps) => {
  const artistsData = transformArtistsData(profile);
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: "start",
    containScroll: "trimSnaps",
    dragFree: false,
    loop: true,
  });

  const [selectedIndex, setSelectedIndex] = useState(0);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  // Set up embla event listeners
  useEffect(() => {
    if (!emblaApi) return;
    onSelect();
    emblaApi.on("select", onSelect);
    emblaApi.on("reInit", onSelect);
  }, [emblaApi, onSelect]);

  if (!artistsData.enabled) {
    return null;
  }

  return (
    <section className="py-16 sm:py-24 bg-black mx-auto max-w-7xl" id="artists">
      <div className="px-4 sm:px-6 lg:px-8 flex flex-col items-center">
        <div className="text-center mb-12">
          <h2 className="text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-4">
            {artistsData.title}
          </h2>
        </div>

        <div className="relative w-full overflow-hidden">
          <div className="overflow-hidden rounded-xl" ref={emblaRef}>
            <div className="flex gap-3 sm:gap-4 lg:gap-6 ml-0">
              {artistsData.artists.map((artist, index) => (
                <div
                  key={`${artist.name}-${index}`}
                  className="flex-[0_0_calc(100%-2rem)] sm:flex-[0_0_calc(70%-1rem)] md:flex-[0_0_calc(50%-1rem)] lg:flex-[0_0_360px] min-w-0 max-w-full mx-auto"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <ArtistProfile
                    imageUrl={artist.imageUrl}
                    name={artist.name}
                    specialty={artist.specialty}
                    url={artist.url}
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Progress indicator */}
          {artistsData.artists.length > 1 && (
            <div className="flex justify-center mt-8 gap-2">
              {artistsData.artists.map((_, index) => (
                <div
                  key={index}
                  className="w-2 h-2 rounded-full transition-all duration-300 cursor-pointer bg-white"
                  style={{
                    opacity: selectedIndex === index ? 1 : 0.4,
                    transform:
                      selectedIndex === index ? "scale(1.25)" : "scale(1)",
                  }}
                  onMouseEnter={(e) => {
                    if (selectedIndex !== index) {
                      e.currentTarget.style.opacity = "0.6";
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (selectedIndex !== index) {
                      e.currentTarget.style.opacity = "0.4";
                    }
                  }}
                  onClick={() => emblaApi?.scrollTo(index)}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default ArtistsSection;
