"use client";

import React, { useCallback, useEffect, useState } from "react";
import useEmblaCarousel from "embla-carousel-react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { getIconComponent } from "@/lib/iconUtils";
import { UserProfile } from "@/types/user";
import { transformLinksData } from "@/lib/brokenInkUtils";
import { cn } from "@/lib/utils";

interface LinksSectionProps {
  profile: UserProfile;
}

const LinksSection = ({ profile }: LinksSectionProps) => {
  const linksData = transformLinksData(profile);
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: "start",
    containScroll: "trimSnaps",
    dragFree: false,
    loop: false,
    skipSnaps: false,
  });

  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setCanScrollPrev(emblaApi.canScrollPrev());
    setCanScrollNext(emblaApi.canScrollNext());
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;
    onSelect();
    emblaApi.on("select", onSelect);
    emblaApi.on("reInit", onSelect);

    // Simulate loading
    const timer = setTimeout(() => setIsLoading(false), 500);
    return () => clearTimeout(timer);
  }, [emblaApi, onSelect]);

  // Enhanced link click handler
  const handleLinkClick = useCallback(
    async (url: string, event: React.MouseEvent) => {
      try {
        if (
          url.startsWith("http") ||
          url.startsWith("mailto:") ||
          url.startsWith("tel:")
        ) {
          window.open(url, "_blank", "noopener,noreferrer");
        } else if (url.startsWith("#")) {
          event.preventDefault();
          const element = document.querySelector(url);
          if (element) {
            element.scrollIntoView({
              behavior: "smooth",
              block: "start",
              inline: "nearest",
            });
          }
        }
      } catch (error) {
        console.error("Error opening link:", error);
      }
    },
    []
  );

  // Don't render if no links and not loading
  if (!isLoading && linksData.links.length === 0) {
    return null;
  }

  return (
    <section className="py-16 sm:py-24" id="links">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-4">
            {linksData.title}
          </h2>
          {linksData.description && (
            <p className="text-gray-300 text-lg max-w-2xl mx-auto">
              {linksData.description}
            </p>
          )}
        </div>

        {/* Desktop Grid Layout */}
        <div className="hidden lg:grid lg:grid-cols-3 xl:grid-cols-4 gap-6 justify-items-center">
          {linksData.links.map((link, index) => {
            const IconComponent = getIconComponent(link.iconName);
            return (
              <LinkCard
                key={index}
                icon={<IconComponent className="h-6 w-6 text-white" />}
                title={link.text}
                description={link.description}
                onClick={(e) => handleLinkClick(link.url, e)}
                isLoading={isLoading}
              />
            );
          })}
        </div>

        {/* Mobile/Tablet Horizontal Scroll */}
        <div className="lg:hidden relative">
          <div className="overflow-hidden" ref={emblaRef}>
            <div className="flex gap-4 touch-pan-y">
              {linksData.links.map((link, index) => {
                const IconComponent = getIconComponent(link.iconName);
                return (
                  <div
                    key={index}
                    className="flex-none w-[280px] sm:w-[320px]"
                  >
                    <LinkCard
                      icon={<IconComponent className="h-6 w-6 text-white" />}
                      title={link.text}
                      description={link.description}
                      onClick={(e) => handleLinkClick(link.url, e)}
                      isLoading={isLoading}
                    />
                  </div>
                );
              })}
            </div>
          </div>

          {/* Navigation Buttons */}
          {!isLoading && linksData.links.length > 1 && (
            <>
              <Button
                variant="outline"
                size="icon"
                className={cn(
                  "absolute left-2 top-1/2 -translate-y-1/2 z-10",
                  "bg-black/40 border-white/20 text-white hover:bg-black/60",
                  "backdrop-blur-sm transition-all duration-200",
                  !canScrollPrev && "opacity-50 cursor-not-allowed"
                )}
                onClick={scrollPrev}
                disabled={!canScrollPrev}
                aria-label="Link anterior"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                className={cn(
                  "absolute right-2 top-1/2 -translate-y-1/2 z-10",
                  "bg-black/40 border-white/20 text-white hover:bg-black/60",
                  "backdrop-blur-sm transition-all duration-200",
                  !canScrollNext && "opacity-50 cursor-not-allowed"
                )}
                onClick={scrollNext}
                disabled={!canScrollNext}
                aria-label="Próximo link"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </>
          )}

          {/* Scroll Indicators */}
          {!isLoading && linksData.links.length > 1 && (
            <div className="flex justify-center mt-6 gap-2">
              {linksData.links.map((_, index) => (
                <button
                  key={index}
                  className={cn(
                    "w-2 h-2 rounded-full transition-all duration-200",
                    index === selectedIndex
                      ? "bg-white"
                      : "bg-white/30 hover:bg-white/50"
                  )}
                  onClick={() => emblaApi?.scrollTo(index)}
                  aria-label={`Ir para link ${index + 1}`}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

// LinkCard Component
interface LinkCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  onClick: (event: React.MouseEvent) => void;
  isLoading?: boolean;
}

const LinkCard: React.FC<LinkCardProps> = ({
  icon,
  title,
  description,
  onClick,
  isLoading = false,
}) => {
  if (isLoading) {
    return (
      <div className="w-full max-w-sm animate-pulse">
        <div className="rounded-3xl bg-gray-800/50 backdrop-blur-3xl md:w-[340px] w-full max-w-sm p-6 ring-1 ring-black/10">
          <div className="flex flex-col items-center gap-4 text-center">
            <div className="bg-gray-700/50 p-6 rounded-3xl w-16 h-16"></div>
            <div className="space-y-2 w-full">
              <div className="h-6 bg-gray-700/50 rounded w-3/4 mx-auto"></div>
              <div className="h-4 bg-gray-700/50 rounded w-full"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <button
      className="group relative flex flex-col justify-between rounded-3xl bg-custom backdrop-blur-3xl md:w-[340px] w-full max-w-sm p-6 ring-1 ring-black/10 transition-all hover:ring-white/20 hover:scale-[1.02] active:scale-[0.98] text-center"
      onClick={onClick}
    >
      <div className="flex flex-col items-center gap-4">
        <div className="bg-black/40 p-6 rounded-3xl transition-all group-hover:bg-black/60">
          {icon}
        </div>
        <div>
          <h3 className="text-white text-xl font-bold mb-2">{title}</h3>
          <p className="text-gray-400 text-sm leading-relaxed">{description}</p>
        </div>
      </div>
    </button>
  );
};

export default LinksSection;
