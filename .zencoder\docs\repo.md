# AvencaLink Information

## Summary

AvencaLink is a modern, responsive Linktree-style web application built with Next.js 15, TypeScript, and Tailwind CSS. It allows users to create personalized profile pages with custom usernames, manage links, and track analytics. The application features dynamic user profiles, responsive design, link management, custom themes, social media integration, and analytics tracking.

## Structure

- **app/**: Next.js App Router with dynamic user profile pages, API routes, and authentication pages
- **components/**: React components including UI components, link cards, and user profiles
- **hooks/**: Custom React hooks for performance and user profiles
- **lib/**: Utility functions for various features
- **services/**: API service layer for data handling
- **types/**: TypeScript type definitions
- **public/**: Static assets and sample data

## Language & Runtime

**Language**: TypeScript
**Version**: TypeScript 5.8.3
**Framework**: Next.js 15.3.5
**Package Manager**: PNPM (as specified in README)

## Dependencies

**Main Dependencies**:

- React 19.1.0 and React DOM 19.1.0
- Next.js 15.3.5 with App Router
- Radix UI component library (various components)
- TanStack React Query 5.81.5
- Tailwind CSS 4.1.11
- Framer Motion 12.23.0
- Zod 3.25.76 for validation
- Leaflet 1.9.4 and React Leaflet 5.0.0 for maps
- Next Themes 0.4.6 for theming

**Development Dependencies**:

- ESLint 9.30.1 with Next.js configuration
- TypeScript 5.8.3
- PostCSS 8.5.6
- Tailwind CSS 4.1.11

## Build & Installation

```bash
# Install dependencies
pnpm install

# Development server
pnpm dev

# Build for production
pnpm build

# Start production server
pnpm start

# Lint code
pnpm lint
```

## Main Files & Resources

**Entry Points**:

- `app/page.tsx`: Main application homepage
- `app/[username]/page.tsx`: Dynamic user profile pages
- `app/login/page.tsx`: Authentication page
- `app/signup/page.tsx`: User registration page

**Configuration Files**:

- `next.config.ts`: Next.js configuration
- `tsconfig.json`: TypeScript configuration
- `tailwind.config.ts`: Tailwind CSS configuration
- `package.json`: Project dependencies and scripts

**API Routes**:

- `/api/users/[username]`: Get user profile data
- `/api/users/[username]/view`: Track profile view
- `/api/links/[linkId]/click`: Track link click
- `/api/stats`: Get platform statistics

## Responsive Design

The application is built with a mobile-first approach using Tailwind CSS breakpoints:

- Mobile: Default (< 640px)
- Tablet: `sm:` (≥ 640px)
- Desktop: `md:` (≥ 768px)
- Large: `lg:` (≥ 1024px)

## Deployment

The application is designed to be deployed on Vercel (recommended) but can also be deployed to other platforms that support Next.js, including Netlify, Railway, DigitalOcean App Platform, and AWS Amplify.
