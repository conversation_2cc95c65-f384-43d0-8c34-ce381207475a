import React from "react";

interface SocialCardProps {
  icon: React.ReactNode;
  platformName: string;
  description: string;
  href?: string;
}

const SocialCard: React.FC<SocialCardProps> = ({
  icon,
  platformName,
  description,
  href = "#",
}) => {
  return (
    <a
      className="group relative flex flex-col justify-between rounded-3xl bg-custom backdrop-blur-3xl md:w-[340px] w-full max-w-sm sm:max-w-xs p-6 ring-1 ring-black/10 transition-all hover:ring-white/20"
      href={href}
      target="_blank"
      rel="noopener noreferrer"
    >
      <div className="flex items-center gap-4 sm:flex-col sm:items-center sm:gap-3">
        <div className="bg-black/40 p-6 rounded-3xl">{icon}</div>
        <div className="sm:text-center">
          <p className="text-lg font-semibold text-white">{platformName}</p>
          <p className="mt-2 text-gray-400 sm:mt-2">{description}</p>
        </div>
      </div>
    </a>
  );
};

export default SocialCard;
