import React from "react";
import { getIconComponent } from "@/lib/iconUtils";
import { UserProfile } from "@/types/user";
import {
  transformContactData,
  transformLocationData,
} from "@/lib/brokenInkUtils";

interface ContactSectionProps {
  profile: UserProfile;
}

const ContactSection = ({ profile }: ContactSectionProps) => {
  const contactData = transformContactData(profile);
  const locationData = transformLocationData(profile);

  // Extract email from links
  const emailLink = contactData.links.find((link) =>
    link.url.startsWith("mailto:")
  );
  const email = emailLink?.url.replace("mailto:", "") || "<EMAIL>";

  return (
    <section className="py-16 sm:py-24 bg-black" id="contact">
      <div className="container mx-auto px-4">
        {/* Hours Information */}
        {locationData.hours && (
          <div className="space-y-2 mt-16">
            <h3 className="text-white text-xl font-semibold mb-4 flex justify-center">
              <PERSON>r<PERSON><PERSON> de Funcionamento
            </h3>
            {locationData.hours.weekdays && (
              <div className="flex justify-center gap-4">
                <span className="text-gray-400">Segunda a Sexta:</span>
                <span className="text-gray-300">
                  {locationData.hours.weekdays}
                </span>
              </div>
            )}
            {locationData.hours.weekends && (
              <div className="flex justify-center gap-4">
                <span className="text-gray-400">Sábado:</span>
                <span className="text-gray-300">
                  {locationData.hours.weekends}
                </span>
              </div>
            )}
            {locationData.hours.closed && (
              <div className="flex justify-center gap-4">
                <span className="text-gray-400">
                  {locationData.hours.closed}:
                </span>
                <span className="text-gray-300">Fechado</span>
              </div>
            )}
          </div>
        )}

        {/* Location Information */}
        {locationData.enabled && (
          <div className="text-center my-16">
            <h2 className="text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-4">
              {locationData.title}
            </h2>
            {locationData.description && (
              <p className="text-gray-300 text-lg max-w-2xl mx-auto">
                {locationData.description}
              </p>
            )}
            {locationData.address && (
              <p className="text-gray-400 text-base mt-2">
                {locationData.address}
              </p>
            )}

            {/* Google Maps Link */}
            {locationData.googleMapsUrl && (
              <div className="text-center mt-6">
                <a
                  href={locationData.googleMapsUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-2 px-6 py-3 rounded-lg transition-colors text-gray-300 hover:text-white"
                >
                  Ver no Google Maps
                  <i className="fa fa-map-marker"></i>
                </a>
              </div>
            )}
          </div>
        )}

        {/* Contact Information */}
        <div className="text-center">
          <h2 className="text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-6">
            {profile.user.name}
          </h2>
          <div className="text-gray-300 text-lg space-y-2">
            {emailLink && (
              <p>
                <a
                  className="hover:text-white transition-colors"
                  href={emailLink.url}
                >
                  {email}
                </a>
              </p>
            )}
          </div>
          {contactData.socialMedia.length > 0 && (
            <div className="mt-12">
              <div className="flex justify-center gap-6">
                {contactData.socialMedia.map((social) => {
                  const iconName = social.classIcon?.includes("fa-")
                    ? social.classIcon.split("fa-")[1]
                    : "globe";
                  const IconComponent = getIconComponent(iconName);
                  return (
                    <a
                      key={social.text}
                      className="text-gray-400 hover:text-white transition-colors"
                      href={social.url}
                      aria-label={social.text}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <IconComponent className="h-7 w-7" />
                    </a>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
