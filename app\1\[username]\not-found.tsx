import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft, UserX } from "lucide-react";

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background via-muted/20 to-background p-4">
      <div className="w-full max-w-2xl">
        {/* Animated 404 Section */}
        <div className="text-center mb-12 animate-fade-in">
          <div className="relative mb-8">
            <div className="flex items-center justify-center mb-4">
              <UserX className="w-16 h-16 text-muted-foreground/30" />
            </div>
            <h1 className="text-8xl md:text-9xl font-black text-transparent bg-gradient-to-r from-primary via-primary/80 to-primary/60 bg-clip-text animate-pulse">
              404
            </h1>
            <div className="absolute inset-0 text-8xl md:text-9xl font-black text-primary/10 blur-sm">
              404
            </div>
          </div>

          <div className="space-y-4 animate-slide-up">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground">
              Perfil Não Encontrado
            </h2>
            <p className="text-lg text-muted-foreground max-w-lg mx-auto leading-relaxed">
              O perfil que você está procurando não existe ou foi desativado.
            </p>
          </div>
        </div>

        {/* Action Button */}
        <div className="text-center animate-slide-up">
          <Button asChild size="lg" animation="hover" className="min-w-48">
            <Link href="/">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Voltar ao Início
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
