import React from "react";

interface ArtistProfileProps {
  imageUrl: string;
  name: string;
  specialty: string;
  url?: string;
}

const ArtistProfile: React.FC<ArtistProfileProps> = ({
  imageUrl,
  name,
  specialty,
  url,
}) => {
  const content = (
    <div className="flex flex-col items-center gap-4 transition-transform duration-300 hover:scale-105">
      <div
        className="w-40 h-40 bg-center bg-cover rounded-full border-6 border-gray-400 hover:border-gray-400 transition-colors duration-300"
        style={{ backgroundImage: `url("${imageUrl}")` }}
        role="img"
        aria-label={`${name}, tattoo artist`}
      ></div>
      <div className="md:w-[340px] w-full max-w-sm sm:max-w-xs text-center">
        <p className="text-white text-xl font-bold text-center">{name}</p>
        <p className="text-gray-400">{specialty}</p>
      </div>
    </div>
  );

  if (url && url !== "#") {
    return (
      <a
        href={url}
        target="_blank"
        rel="noopener noreferrer"
        className="block cursor-pointer"
        aria-label={`Visitar perfil de ${name}`}
      >
        {content}
      </a>
    );
  }

  return content;
};

export default ArtistProfile;
